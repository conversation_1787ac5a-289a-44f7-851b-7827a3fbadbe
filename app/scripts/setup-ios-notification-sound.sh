#!/bin/bash

# Setup iOS Notification Sound Script
# This script helps configure the custom notification sound for iOS

set -e

echo "🔊 Setting up iOS Custom Notification Sound"
echo "============================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the app root directory"
    exit 1
fi

# Check if sound file exists
if [ ! -f "ios/notification_158187.mp3" ]; then
    echo "📁 Copying sound file to iOS directory..."
    cp android/app/src/main/res/raw/notification_158187.mp3 ios/notification_158187.mp3
    echo "✅ Sound file copied to ios/notification_158187.mp3"
else
    echo "✅ Sound file already exists at ios/notification_158187.mp3"
fi

# Copy sound files to all iOS target directories
echo "📁 Copying sound files to iOS target directories..."
cp ios/notification_158187.mp3 ios/bppulse/notification_158187.mp3
cp ios/notification_158187.mp3 ios/aralpulse/notification_158187.mp3
cp ios/notification_158187.mp3 ios/bpUSpulse/notification_158187.mp3

# Copy to Resources/sounds directories
cp ios/notification_158187.mp3 ios/bppulse/Resources/sounds/notification_158187.mp3
cp ios/notification_158187.mp3 ios/aralpulse/Resources/sounds/notification_158187.mp3
cp ios/notification_158187.mp3 ios/bpUSpulse/Resources/sounds/notification_158187.mp3

echo "✅ Sound files copied to all iOS target directories"

echo ""
echo "📱 Next Steps - Manual Xcode Configuration Required:"
echo "=================================================="
echo ""
echo "1. Open Xcode workspace:"
echo "   cd ios && open bppulse.xcworkspace"
echo ""
echo "2. Add sound file to project:"
echo "   - Right-click on 'bppulse' project in Xcode navigator"
echo "   - Select 'Add Files to \"bppulse\"'"
echo "   - Choose 'notification_158187' from the ios folder"
echo "   - Click 'Add'"
echo ""
echo "3. Verify target membership:"
echo "   - Select 'notification_158187' in Xcode"
echo "   - In the right panel, check 'Target Membership'"
echo "   - Ensure both 'bppulse' targets are checked"
echo ""
echo "4. Test the configuration:"
echo "   - Build and run the app"
echo "   - Send a test notification from Airship dashboard"
echo "   - Include this in the notification payload:"
echo "     {\"ios\": {\"sound\": \"notification_158187\"}}"
echo ""
echo "📋 Airship Dashboard Configuration:"
echo "=================================="
echo ""
echo "For Android notifications:"
echo "  {\"android\": {\"sound\": \"charge_start\"}}"
echo ""
echo "For iOS notifications:"
echo "  {\"ios\": {\"sound\": \"notification_158187\"}}"
echo ""
echo "For both platforms:"
echo "  {"
echo "    \"notification\": {"
echo "      \"alert\": \"Your message\","
echo "      \"android\": {\"sound\": \"charge_start\"},"
echo "      \"ios\": {\"sound\": \"notification_158187\"}"
echo "    }"
echo "  }"
echo ""
echo "🎉 Setup complete! Follow the manual steps above to finish iOS configuration."
