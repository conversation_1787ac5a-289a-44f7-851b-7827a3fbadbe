#!/bin/bash

# Android Notification Testing Script
# This script helps test and debug Android custom notification sounds

echo "🔊 Android Custom Notification Sound Testing"
echo "============================================="
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the app root directory"
    exit 1
fi

echo "📱 ANDROID SETUP VERIFICATION:"
echo "==============================="

# Check sound file
if [ -f "android/app/src/main/res/raw/notification_158187.mp3" ]; then
    echo "✅ Sound file exists: android/app/src/main/res/raw/notification_158187.mp3"
    
    # Get file size
    size=$(stat -f%z "android/app/src/main/res/raw/notification_158187.mp3" 2>/dev/null || stat -c%s "android/app/src/main/res/raw/notification_158187.mp3" 2>/dev/null)
    echo "   File size: ${size} bytes"
else
    echo "❌ Sound file missing: android/app/src/main/res/raw/notification_158187.mp3"
fi

# Check MainApplication.java for notification channel
if grep -q "createNotificationChannel" "android/app/src/main/java/com/aml/evapp/MainApplication.java"; then
    echo "✅ Notification channel creation found in MainApplication.java"
else
    echo "❌ Notification channel creation missing in MainApplication.java"
fi

# Check for channel ID
if grep -q "bp_pulse_notifications" "android/app/src/main/java/com/aml/evapp/MainApplication.java"; then
    echo "✅ Channel ID 'bp_pulse_notifications' configured"
else
    echo "❌ Channel ID 'bp_pulse_notifications' not found"
fi

# Check for custom sound reference
if grep -q "R.raw.notification_158187" "android/app/src/main/java/com/aml/evapp/MainApplication.java"; then
    echo "✅ Custom sound reference found in notification channel"
else
    echo "❌ Custom sound reference missing in notification channel"
fi

echo ""
echo "🧪 TESTING STEPS:"
echo "================="
echo ""
echo "1. **Build and install the app:**"
echo "   npm run android"
echo ""
echo "2. **Check Android logs for channel creation:**"
echo "   adb logcat | grep 'MainApplication\\|NotificationChannel'"
echo ""
echo "3. **Test with Airship Dashboard:**"
echo "   - Go to Airship Dashboard → Create Message"
echo "   - Create a push notification"
echo "   - Send to your device"
echo "   - The notification should use the custom sound automatically"
echo ""
echo "4. **Alternative: Test with API payload:**"
echo "   Use this payload structure:"
echo "   {"
echo "     \"audience\": \"all\","
echo "     \"notification\": {"
echo "       \"alert\": \"Test notification\""
echo "     },"
echo "     \"device_types\": [\"android\"]"
echo "   }"
echo ""
echo "5. **Check notification channel in Android settings:**"
echo "   - Go to Android Settings → Apps → BP Pulse → Notifications"
echo "   - Look for 'BP Pulse Notifications' channel"
echo "   - Verify sound is set to custom sound"
echo ""

echo "🔧 TROUBLESHOOTING:"
echo "==================="
echo ""
echo "If custom sound still doesn't work:"
echo ""
echo "1. **Clear app data and reinstall:**"
echo "   adb uninstall com.aml.evapp"
echo "   npm run android"
echo ""
echo "2. **Check notification channel in device settings:**"
echo "   Settings → Apps → BP Pulse → Notifications"
echo "   Look for 'BP Pulse Notifications' channel"
echo ""
echo "3. **Verify sound file format:**"
echo "   - File should be .mp3 format"
echo "   - File size should be reasonable (< 1MB)"
echo "   - File should be in android/app/src/main/res/raw/"
echo ""
echo "4. **Check Android logs:**"
echo "   adb logcat | grep -E 'MainApplication|Airship|Notification'"
echo ""

echo "📋 AIRSHIP CONFIGURATION:"
echo "========================="
echo ""
echo "The notification channel is now created automatically with custom sound."
echo "No additional Airship Custom Keys configuration should be needed."
echo ""
echo "If you still want to use Custom Keys (optional):"
echo "- Platform: Android"
echo "- Key: sound"
echo "- Value: notification_158187"
echo ""
echo "🎉 The custom sound should now work automatically for all Android notifications!"
