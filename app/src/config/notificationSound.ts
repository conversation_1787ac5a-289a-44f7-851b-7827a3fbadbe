/**
 * Centralized notification sound configuration
 * Change these values to update notification sounds across the entire app
 */

export const NOTIFICATION_SOUND_CONFIG = {
  // Android configuration
  ANDROID: {
    CHANNEL_DESCRIPTION: 'Notifications for BP Pulse app with custom sound',
    CHANNEL_ID: 'bp_pulse_notifications',
    CHANNEL_NAME: 'BP Pulse Notifications',
    IMPORTANCE: 'HIGH' as const,
    // Resource path for Android (R.raw.{SOUND_FILE_NAME})
    RESOURCE_PATH: 'notification_158187', // Must match SOUND_FILE_NAME
  },

  // Default sound settings
  DEFAULTS: {
    ENABLED: true,
    VOLUME: 0.8,
  },

  // iOS configuration
  IOS: {
    // Sound paths to try when loading (relative to bundle)
    SOUND_PATHS: [
      '', // Bundle root without extension
      '.mp3', // Bundle root with extension
      'sounds/', // sounds subdirectory without extension
      'sounds/.mp3', // sounds subdirectory with extension
      'Resources/sounds/', // Full path without extension
      'Resources/sounds/.mp3', // Full path with extension
    ],
  },

  // Logging configuration
  LOGGING: {
    ENABLED: __DEV__, // Only log in development
    VERBOSE: false, // Set to true for detailed path logging
  },

  // Main notification sound file (without extension)
  SOUND_FILE_NAME: 'notification_158187', // Change this to update everywhere
} as const;

/**
 * Get the full sound file name with extension
 */
export const getSoundFileName = (withExtension = false): string => {
  return withExtension
    ? `${NOTIFICATION_SOUND_CONFIG.SOUND_FILE_NAME}.mp3`
    : NOTIFICATION_SOUND_CONFIG.SOUND_FILE_NAME;
};

/**
 * Get Android resource identifier
 */
export const getAndroidResourceId = (): string => {
  return NOTIFICATION_SOUND_CONFIG.ANDROID.RESOURCE_PATH;
};

/**
 * Get iOS sound paths with the actual filename
 */
export const getIOSSoundPaths = (): string[] => {
  const fileName = NOTIFICATION_SOUND_CONFIG.SOUND_FILE_NAME;
  return NOTIFICATION_SOUND_CONFIG.IOS.SOUND_PATHS.map(path => {
    if (path.endsWith('.mp3')) {
      return path.replace('.mp3', `${fileName}.mp3`);
    } else if (path.endsWith('/')) {
      return `${path}${fileName}`;
    } else {
      return `${path}${fileName}`;
    }
  });
};

/**
 * Get Airship notification configuration
 */
export const getAirshipNotificationConfig = () => ({
  android: {
    sound: NOTIFICATION_SOUND_CONFIG.SOUND_FILE_NAME,
    //channel_id: NOTIFICATION_SOUND_CONFIG.ANDROID.CHANNEL_ID,
  },
  ios: {
    sound: NOTIFICATION_SOUND_CONFIG.SOUND_FILE_NAME,
  },
});

/**
 * Conditional logging utility
 */
export const soundLogger = {
  info: (message: string, ...args: any[]) => {
    if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
      console.log(`[NotificationSound] ${message}`, ...args);
    }
  },
  verbose: (message: string, ...args: any[]) => {
    if (
      NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED &&
      NOTIFICATION_SOUND_CONFIG.LOGGING.VERBOSE
    ) {
      console.log(`[NotificationSound:Verbose] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: any[]) => {
    if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
      console.warn(`[NotificationSound] ${message}`, ...args);
    }
  },
};
