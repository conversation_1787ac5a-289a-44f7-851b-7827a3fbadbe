import Airship, {
  PermissionStatus,
  PushNotificationStatus,
} from '@ua/react-native-airship';
import { logger } from '@utils/logger';

import { AirshipAnalyticsService } from './airship';

export class PushNotificationConsentService {
  // Checks Native Permission for Push Notification
  public async isPushNotificationAllowedByUser(): Promise<boolean> {
    const currentNotificationConsent: PushNotificationStatus =
      await Airship.push.getNotificationStatus();
    const notificationPermissionStatus =
      currentNotificationConsent.notificationPermissionStatus;
    logger.info(
      'Device Push Notification Constent : ',
      notificationPermissionStatus,
    );
    return notificationPermissionStatus === PermissionStatus.Granted;
  }

  // Checks Salesforce Consent for Push Notification
  public async isSalesForcePushConsentAccepted(
    consentData: any,
  ): Promise<boolean> {
    const pushNotificationConstentAccepted = consentData.some(
      (consent: { channel: string; accepted: boolean }) =>
        consent.channel === 'Push' && consent.accepted === true,
    );
    logger.info(
      'SalesForce Push Notification Constent Acceptance',
      pushNotificationConstentAccepted,
    );

    // Set the custom attribute in Airship
    await AirshipAnalyticsService.setSalesforcePushConsentAttribute(
      pushNotificationConstentAccepted,
    );

    return pushNotificationConstentAccepted;
  }

  public async nativePermissionStatus(): Promise<PushNotificationStatus> {
    return await Airship.push.getNotificationStatus();
  }
  // Enable Airship notifications without triggering native permission dialog
  // This should only be called after custom consent flow is completed
  public async enableAirshipNotifications(): Promise<void> {
    try {
      logger.info('Enabling Airship notifications...');

      // Enable Airship notifications - this won't trigger native dialog
      // if permissions are already granted through custom flow
      const allowed = await this.isPushNotificationAllowedByUser();
      if (allowed) {
        await Airship.push.setUserNotificationsEnabled(true);
      } else {
        logger.warn('Attempted to enable Airship notifications without permission');
      }

      logger.info('Airship notifications enabled');
    } catch (error) {
      logger.error('Error enabling Airship notifications:', error);
    }
  }
}
