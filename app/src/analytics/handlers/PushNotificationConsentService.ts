import Airship, {
  PermissionStatus,
  PushNotificationStatus,
} from '@ua/react-native-airship';
import { logger } from '@utils/logger';

import { AirshipAnalyticsService } from './airship';

export class PushNotificationConsentService {
  // Checks Native Permission for Push Notification
  public async isPushNotificationAllowedByUser(): Promise<boolean> {
    const currentNotificationConsent: PushNotificationStatus =
      await Airship.push.getNotificationStatus();
    const notificationPermissionStatus =
      currentNotificationConsent.notificationPermissionStatus;
    logger.info(
      'Device Push Notification Constent : ',
      notificationPermissionStatus,
    );
    return notificationPermissionStatus === PermissionStatus.Granted;
  }

  // Checks Salesforce Consent for Push Notification
  public async isSalesForcePushConsentAccepted(
    consentData: any,
  ): Promise<boolean> {
    const pushNotificationConstentAccepted = consentData.some(
      (consent: { channel: string; accepted: boolean }) =>
        consent.channel === 'Push' && consent.accepted === true,
    );
    logger.info(
      'SalesForce Push Notification Constent Acceptance',
      pushNotificationConstentAccepted,
    );

    // Set the custom attribute in Airship
    await AirshipAnalyticsService.setSalesforcePushConsentAttribute(
      pushNotificationConstentAccepted,
    );

    return pushNotificationConstentAccepted;
  }

  public async nativePermissionStatus(): Promise<PushNotificationStatus> {
    return await Airship.push.getNotificationStatus();
  }

  // Check push notification permission status without triggering native dialog
  public async checkPushNotificationPermission(): Promise<boolean> {
    try {
      logger.info('Checking push notification permission status...');

      // Only check the current status, don't trigger permission request
      const status = await Airship.push.getNotificationStatus();
      const isGranted =
        status.notificationPermissionStatus === PermissionStatus.Granted;

      logger.info('Push notification permission status:', isGranted);
      return isGranted;
    } catch (error) {
      logger.error('Error checking push notification permission:', error);
      return false;
    }
  }

  // Enable Airship notifications without triggering native permission dialog
  // This should only be called after custom consent flow is completed
  public async enableAirshipNotifications(): Promise<void> {
    try {
      logger.info('Enabling Airship notifications...');

      // Enable Airship notifications - this won't trigger native dialog
      // if permissions are already granted through custom flow
      await Airship.push.setUserNotificationsEnabled(true);

      logger.info('Airship notifications enabled');
    } catch (error) {
      logger.error('Error enabling Airship notifications:', error);
    }
  }
}
