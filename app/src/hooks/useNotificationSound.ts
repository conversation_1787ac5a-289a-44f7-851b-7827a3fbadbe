import { useCallback, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';

import {
  getSoundFileName,
  NOTIFICATION_SOUND_CONFIG,
  soundLogger,
} from '../config/notificationSound';

interface UseNotificationSoundOptions {
  soundFileName?: string;
  enabled?: boolean;
}

interface UseNotificationSoundReturn {
  playSound: () => void;
  isLoaded: boolean;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

/**
 * Custom hook for playing notification sounds
 * @param options Configuration options for the sound
 * @returns Object with playSound function and loading state
 */
export const useNotificationSound = (
  options: UseNotificationSoundOptions = {},
): UseNotificationSoundReturn => {
  const {
    soundFileName = getSoundFileName(),
    enabled = NOTIFICATION_SOUND_CONFIG.DEFAULTS.ENABLED,
  } = options;

  const [isLoaded, setIsLoaded] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);

  // Play notification sound with error handling
  const playSound = useCallback(() => {
    if (isLoaded && isEnabled) {
      try {
        if (Platform.OS === 'ios') {
          // For iOS, use PushNotificationIOS to play a local notification sound
          PushNotificationIOS.addNotificationRequest({
            id: `sound_${Date.now()}`,
            title: '',
            body: '',
            sound: `${soundFileName}.mp3`,
            badge: 0,
            userInfo: {},
          });
        } else {
          // For Android, use react-native-push-notification to play a local notification
          PushNotification.localNotification({
            title: '',
            message: '',
            playSound: true,
            soundName: `${soundFileName}.mp3`,
            autoCancel: true,
            largeIcon: '',
            smallIcon: 'ic_notification',
            bigText: '',
            subText: '',
            vibrate: false,
            ongoing: false,
            priority: 'default',
            visibility: 'private',
            importance: 'default',
            allowWhileIdle: false,
            ignoreInForeground: false,
            shortcutId: undefined,
            onlyAlertOnce: false,
            when: undefined,
            usesChronometer: false,
            timeoutAfter: undefined,
            messageId: undefined,
            actions: undefined,
            invokeApp: false,
            tag: undefined,
            group: undefined,
            groupSummary: false,
            showWhen: undefined,
            number: undefined,

            color: undefined,
          });
        }

        soundLogger.info(`Notification sound played: ${soundFileName} (${Platform.OS})`);
      } catch (error) {
        soundLogger.warn('Failed to play notification sound:', error);
      }
    } else {
      soundLogger.verbose(
        `Sound playback skipped - loaded: ${isLoaded}, enabled: ${isEnabled}`,
      );
    }
  }, [isLoaded, isEnabled, soundFileName]);

  // Initialize push notification configuration
  useEffect(() => {
    const initializePushNotifications = () => {
      try {
        if (Platform.OS === 'android') {
          // Configure Android push notifications
          PushNotification.configure({
            onRegister: function (token) {
              soundLogger.verbose('Push notification token:', token);
            },
            onNotification: function (notification) {
              soundLogger.verbose('Push notification received:', notification);
              // Complete the notification for iOS
              if (Platform.OS === 'ios') {
                notification.finish(PushNotificationIOS.FetchResult.NoData);
              }
            },
            onAction: function (notification) {
              soundLogger.verbose('Push notification action:', notification.action);
            },
            onRegistrationError: function (err) {
              soundLogger.warn('Push notification registration error:', err);
            },
            permissions: {
              alert: true,
              badge: true,
              sound: true,
            },
            popInitialNotification: true,
            requestPermissions: false, // Don't request permissions automatically
          });

          // Create notification channel for Android with custom sound
          PushNotification.createChannel(
            {
              channelId: NOTIFICATION_SOUND_CONFIG.ANDROID.CHANNEL_ID,
              channelName: NOTIFICATION_SOUND_CONFIG.ANDROID.CHANNEL_NAME,
              channelDescription: NOTIFICATION_SOUND_CONFIG.ANDROID.CHANNEL_DESCRIPTION,
              playSound: true,
              soundName: `${soundFileName}.mp3`,
              importance: 4, // HIGH
              vibrate: true,
            },
            (created) => {
              soundLogger.info(`Android notification channel created: ${created}`);
            }
          );
        }

        setIsLoaded(true);
        soundLogger.info(
          `Push notification sound configured: ${soundFileName} (${Platform.OS})`,
        );
      } catch (error) {
        soundLogger.warn('Failed to configure push notifications:', error);
        setIsLoaded(false);
        setIsEnabled(false);
      }
    };

    initializePushNotifications();

    return () => {
      // Cleanup if needed
      setIsLoaded(false);
    };
  }, [soundFileName]);

  const setEnabled = useCallback((enableSound: boolean) => {
    setIsEnabled(enableSound);
  }, []);

  return {
    playSound,
    isLoaded,
    isEnabled,
    setEnabled,
  };
};
