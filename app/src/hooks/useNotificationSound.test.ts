import { renderHook } from '@testing-library/react-hooks';
import { Platform } from 'react-native';
import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';

import { useNotificationSound } from './useNotificationSound';

// Mock the dependencies
jest.mock('react-native-push-notification', () => ({
  configure: jest.fn(),
  createChannel: jest.fn(),
  localNotification: jest.fn(),
}));

jest.mock('@react-native-community/push-notification-ios', () => ({
  addNotificationRequest: jest.fn(),
  FetchResult: {
    NoData: 'NoData',
  },
}));

jest.mock('../config/notificationSound', () => ({
  getSoundFileName: jest.fn(() => 'notification_158187'),
  NOTIFICATION_SOUND_CONFIG: {
    DEFAULTS: {
      ENABLED: true,
    },
    ANDROID: {
      CHANNEL_ID: 'bp_pulse_notifications',
      CHANNEL_NAME: 'BP Pulse Notifications',
      CHANNEL_DESCRIPTION: 'Notifications for BP Pulse app with custom sound',
    },
  },
  soundLogger: {
    info: jest.fn(),
    verbose: jest.fn(),
    warn: jest.fn(),
  },
}));

describe('useNotificationSound', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useNotificationSound());

    expect(result.current.isEnabled).toBe(true);
    expect(result.current.isLoaded).toBe(true);
    expect(typeof result.current.playSound).toBe('function');
    expect(typeof result.current.setEnabled).toBe('function');
  });

  it('should configure Android push notifications', () => {
    Platform.OS = 'android';
    
    renderHook(() => useNotificationSound());

    expect(PushNotification.configure).toHaveBeenCalledWith(
      expect.objectContaining({
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },
        requestPermissions: false,
      })
    );

    expect(PushNotification.createChannel).toHaveBeenCalledWith(
      expect.objectContaining({
        channelId: 'bp_pulse_notifications',
        channelName: 'BP Pulse Notifications',
        playSound: true,
        soundName: 'notification_158187.mp3',
      }),
      expect.any(Function)
    );
  });

  it('should play sound on iOS using PushNotificationIOS', () => {
    Platform.OS = 'ios';
    
    const { result } = renderHook(() => useNotificationSound());
    
    result.current.playSound();

    expect(PushNotificationIOS.addNotificationRequest).toHaveBeenCalledWith(
      expect.objectContaining({
        title: '',
        body: '',
        sound: 'notification_158187.mp3',
        badge: 0,
      })
    );
  });

  it('should play sound on Android using PushNotification', () => {
    Platform.OS = 'android';
    
    const { result } = renderHook(() => useNotificationSound());
    
    result.current.playSound();

    expect(PushNotification.localNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        title: '',
        message: '',
        playSound: true,
        soundName: 'notification_158187.mp3',
        autoCancel: true,
      })
    );
  });

  it('should not play sound when disabled', () => {
    const { result } = renderHook(() => useNotificationSound());
    
    result.current.setEnabled(false);
    result.current.playSound();

    expect(PushNotificationIOS.addNotificationRequest).not.toHaveBeenCalled();
    expect(PushNotification.localNotification).not.toHaveBeenCalled();
  });

  it('should accept custom sound file name', () => {
    const customSoundName = 'custom_sound';
    
    renderHook(() => useNotificationSound({ soundFileName: customSoundName }));

    if (Platform.OS === 'android') {
      expect(PushNotification.createChannel).toHaveBeenCalledWith(
        expect.objectContaining({
          soundName: `${customSoundName}.mp3`,
        }),
        expect.any(Function)
      );
    }
  });
});
