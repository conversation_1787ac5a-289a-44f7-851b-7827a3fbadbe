PODS:
  - Airship (19.1.2):
    - Airship/Automation (= 19.1.2)
    - Airship/Basement (= 19.1.2)
    - Airship/Core (= 19.1.2)
    - Airship/FeatureFlags (= 19.1.2)
    - Airship/MessageCenter (= 19.1.2)
    - Airship/PreferenceCenter (= 19.1.2)
  - Airship/Automation (19.1.2):
    - Airship/Core
  - Airship/Basement (19.1.2)
  - Airship/Core (19.1.2):
    - Airship/Basement
  - Airship/FeatureFlags (19.1.2):
    - Airship/Core
  - Airship/MessageCenter (19.1.2):
    - Airship/Core
  - Airship/PreferenceCenter (19.1.2):
    - Airship/Core
  - AirshipFrameworkProxy (13.3.1):
    - Airship (= 19.1.2)
  - Apollo (0.50.0):
    - Apollo/Core (= 0.50.0)
  - Apollo/Core (0.50.0)
  - AppsFlyerFramework (6.12.2):
    - Apps<PERSON>lyerFramework/Main (= 6.12.2)
  - AppsFlyerFramework/Main (6.12.2)
  - boost (1.76.0)
  - bp-cardinal-sdk (0.0.78):
    - Apollo (~> 0.50.0)
    - cardinal-mobile-sdk
    - KeychainSwift
    - React-Core
  - cardinal-mobile-sdk (0.3.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.7)
  - FBReactNativeSpec (0.72.7):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.7)
    - RCTTypeSafety (= 0.72.7)
    - React-Core (= 0.72.7)
    - React-jsi (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - Firebase (10.24.0):
    - Firebase/Core (= 10.24.0)
  - Firebase/Analytics (10.24.0):
    - Firebase/Core
  - Firebase/Core (10.24.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.24.0)
  - Firebase/CoreOnly (10.24.0):
    - FirebaseCore (= 10.24.0)
  - Firebase/Crashlytics (10.24.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.24.0)
  - Firebase/RemoteConfig (10.24.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.24.0)
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.24.0):
    - FirebaseAnalytics/AdIdSupport (= 10.24.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.24.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.24.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseRemoteConfig (10.24.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.29.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.1.0):
    - Google-Maps-iOS-Utils/Clustering (= 4.1.0)
    - Google-Maps-iOS-Utils/Geometry (= 4.1.0)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.1.0)
    - Google-Maps-iOS-Utils/Heatmap (= 4.1.0)
    - Google-Maps-iOS-Utils/QuadTree (= 4.1.0)
    - GoogleMaps
  - Google-Maps-iOS-Utils/Clustering (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/Geometry (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/GeometryUtils (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/Heatmap (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/QuadTree (4.1.0):
    - GoogleMaps
  - GoogleAppMeasurement (10.24.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.24.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.24.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.72.7):
    - hermes-engine/Pre-built (= 0.72.7)
  - hermes-engine/Pre-built (0.72.7)
  - KeychainSwift (24.0.0)
  - libevent (2.1.12)
  - lottie-ios (4.3.4)
  - lottie-react-native (6.4.1):
    - lottie-ios (~> 4.3.3)
    - React-Core
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.7)
  - RCTTypeSafety (0.72.7):
    - FBLazyVector (= 0.72.7)
    - RCTRequired (= 0.72.7)
    - React-Core (= 0.72.7)
  - React (0.72.7):
    - React-Core (= 0.72.7)
    - React-Core/DevSupport (= 0.72.7)
    - React-Core/RCTWebSocket (= 0.72.7)
    - React-RCTActionSheet (= 0.72.7)
    - React-RCTAnimation (= 0.72.7)
    - React-RCTBlob (= 0.72.7)
    - React-RCTImage (= 0.72.7)
    - React-RCTLinking (= 0.72.7)
    - React-RCTNetwork (= 0.72.7)
    - React-RCTSettings (= 0.72.7)
    - React-RCTText (= 0.72.7)
    - React-RCTVibration (= 0.72.7)
  - React-callinvoker (0.72.7)
  - React-Codegen (0.72.7):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.7)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.7)
    - React-Core/RCTWebSocket (= 0.72.7)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.7)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.7)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.7):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.7)
    - React-Codegen (= 0.72.7)
    - React-Core/CoreModulesHeaders (= 0.72.7)
    - React-jsi (= 0.72.7)
    - React-RCTBlob
    - React-RCTImage (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.7):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.7)
    - React-debug (= 0.72.7)
    - React-jsi (= 0.72.7)
    - React-jsinspector (= 0.72.7)
    - React-logger (= 0.72.7)
    - React-perflogger (= 0.72.7)
    - React-runtimeexecutor (= 0.72.7)
  - React-debug (0.72.7)
  - React-hermes (0.72.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.7)
    - React-jsi
    - React-jsiexecutor (= 0.72.7)
    - React-jsinspector (= 0.72.7)
    - React-perflogger (= 0.72.7)
  - React-jsi (0.72.7):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.7)
    - React-jsi (= 0.72.7)
    - React-perflogger (= 0.72.7)
  - React-jsinspector (0.72.7)
  - React-logger (0.72.7):
    - glog
  - react-native-airship (21.3.0):
    - AirshipFrameworkProxy (= 13.3.1)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-appsflyer (6.12.2):
    - AppsFlyerFramework (= 6.12.2)
    - React
  - react-native-config (1.4.6):
    - react-native-config/App (= 1.4.6)
  - react-native-config/App (1.4.6):
    - React-Core
  - react-native-encrypted-storage (4.0.3):
    - React-Core
  - react-native-geolocation (3.4.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-get-random-values (1.10.0):
    - React-Core
  - react-native-google-maps (1.8.0):
    - Google-Maps-iOS-Utils (= 4.1.0)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-maps (1.8.0):
    - React-Core
  - react-native-netinfo (11.1.0):
    - React-Core
  - react-native-safe-area-context (4.7.4):
    - React-Core
  - react-native-slider (4.4.3):
    - React-Core
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-webview (13.6.3):
    - React-Core
  - React-NativeModulesApple (0.72.7):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.7)
  - React-RCTActionSheet (0.72.7):
    - React-Core/RCTActionSheetHeaders (= 0.72.7)
  - React-RCTAnimation (0.72.7):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.7)
    - React-Codegen (= 0.72.7)
    - React-Core/RCTAnimationHeaders (= 0.72.7)
    - React-jsi (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - React-RCTAppDelegate (0.72.7):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.7):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.7)
    - React-Core/RCTBlobHeaders (= 0.72.7)
    - React-Core/RCTWebSocket (= 0.72.7)
    - React-jsi (= 0.72.7)
    - React-RCTNetwork (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - React-RCTImage (0.72.7):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.7)
    - React-Codegen (= 0.72.7)
    - React-Core/RCTImageHeaders (= 0.72.7)
    - React-jsi (= 0.72.7)
    - React-RCTNetwork (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - React-RCTLinking (0.72.7):
    - React-Codegen (= 0.72.7)
    - React-Core/RCTLinkingHeaders (= 0.72.7)
    - React-jsi (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - React-RCTNetwork (0.72.7):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.7)
    - React-Codegen (= 0.72.7)
    - React-Core/RCTNetworkHeaders (= 0.72.7)
    - React-jsi (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - React-RCTSettings (0.72.7):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.7)
    - React-Codegen (= 0.72.7)
    - React-Core/RCTSettingsHeaders (= 0.72.7)
    - React-jsi (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - React-RCTText (0.72.7):
    - React-Core/RCTTextHeaders (= 0.72.7)
  - React-RCTVibration (0.72.7):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.7)
    - React-Core/RCTVibrationHeaders (= 0.72.7)
    - React-jsi (= 0.72.7)
    - ReactCommon/turbomodule/core (= 0.72.7)
  - React-rncore (0.72.7)
  - React-runtimeexecutor (0.72.7):
    - React-jsi (= 0.72.7)
  - React-runtimescheduler (0.72.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.7):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.7)
    - React-cxxreact (= 0.72.7)
    - React-jsi (= 0.72.7)
    - React-logger (= 0.72.7)
    - React-perflogger (= 0.72.7)
  - ReactCommon/turbomodule/core (0.72.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.7)
    - React-cxxreact (= 0.72.7)
    - React-jsi (= 0.72.7)
    - React-logger (= 0.72.7)
    - React-perflogger (= 0.72.7)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNBootSplash (5.1.3):
    - React-Core
  - RNCAsyncStorage (1.19.8):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNDeviceInfo (10.11.0):
    - React-Core
  - RNFBAnalytics (19.2.0):
    - Firebase/Analytics (= 10.24.0)
    - React-Core
    - RNFBApp
  - RNFBApp (19.2.0):
    - Firebase/CoreOnly (= 10.24.0)
    - React-Core
  - RNFBCrashlytics (19.2.0):
    - Firebase/Crashlytics (= 10.24.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBRemoteConfig (19.2.0):
    - Firebase/RemoteConfig (= 10.24.0)
    - React-Core
    - RNFBApp
  - RNFileViewer (2.1.5):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.14.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNInAppBrowser (3.7.0):
    - React-Core
  - RNReanimated (3.11.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.27.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNShare (10.0.1):
    - React-Core
  - RNSound (0.11.2):
    - React-Core
    - RNSound/Core (= 0.11.2)
  - RNSound/Core (0.11.2):
    - React-Core
  - RNSVG (14.1.0):
    - React-Core
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - "bp-cardinal-sdk (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/@bp/react-native-cardinal-sdk`)"
  - cardinal-mobile-sdk (from `https://dev.azure.com/bp-digital/DCM_Frameworks/_git/cardinal-mobile-sdk`)
  - DoubleConversion (from `../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - FirebaseABTesting
  - FirebaseCore
  - FirebaseCoreExtension
  - FirebaseInstallations
  - glog (from `../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleDataTransport
  - GoogleUtilities
  - hermes-engine (from `../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - "lottie-react-native (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/lottie-react-native`)"
  - nanopb
  - RCT-Folly (from `../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../node_modules/react-native/`)
  - React-callinvoker (from `../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../node_modules/react-native/`)
  - React-CoreModules (from `../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../../node_modules/react-native/ReactCommon/logger`)
  - "react-native-airship (from `../../node_modules/@ua/react-native-airship`)"
  - react-native-appsflyer (from `../../node_modules/react-native-appsflyer`)
  - react-native-config (from `../../node_modules/react-native-config`)
  - "react-native-encrypted-storage (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-encrypted-storage`)"
  - "react-native-geolocation (from `../../node_modules/@react-native-community/geolocation`)"
  - react-native-geolocation-service (from `../../node_modules/react-native-geolocation-service`)
  - "react-native-get-random-values (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-get-random-values`)"
  - react-native-google-maps (from `../../node_modules/react-native-maps`)
  - "react-native-maps (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-maps`)"
  - "react-native-netinfo (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-community/netinfo`)"
  - "react-native-safe-area-context (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-safe-area-context`)"
  - "react-native-slider (from `../../node_modules/@react-native-community/slider`)"
  - "react-native-tracking-transparency (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-tracking-transparency`)"
  - "react-native-webview (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-webview`)"
  - React-NativeModulesApple (from `../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../../node_modules/rn-fetch-blob`)
  - "RNBootSplash (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-bootsplash`)"
  - "RNCAsyncStorage (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-async-storage/async-storage`)"
  - "RNCMaskedView (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-community/masked-view`)"
  - "RNDeviceInfo (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-device-info`)"
  - "RNFBAnalytics (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBRemoteConfig (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-firebase/remote-config`)"
  - "RNFileViewer (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-file-viewer`)"
  - "RNFS (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-fs`)"
  - "RNGestureHandler (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-gesture-handler`)"
  - "RNInAppBrowser (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-inappbrowser-reborn`)"
  - "RNReanimated (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-reanimated`)"
  - "RNScreens (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-screens`)"
  - RNShare (from `../../node_modules/react-native-share`)
  - RNSound (from `../../node_modules/react-native-sound`)
  - "RNSVG (from `../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-svg`)"
  - Yoga (from `../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Airship
    - AirshipFrameworkProxy
    - Apollo
    - AppsFlyerFramework
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - KeychainSwift
    - libevent
    - lottie-ios
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../../node_modules/react-native/third-party-podspecs/boost.podspec"
  bp-cardinal-sdk:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/@bp/react-native-cardinal-sdk"
  cardinal-mobile-sdk:
    :git: https://dev.azure.com/bp-digital/DCM_Frameworks/_git/cardinal-mobile-sdk
  DoubleConversion:
    :podspec: "../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-08-07-RNv0.72.4-813b2def12bc9df02654b3e3653ae4a68d0572e0
  lottie-react-native:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../../node_modules/react-native/ReactCommon/logger"
  react-native-airship:
    :path: "../../node_modules/@ua/react-native-airship"
  react-native-appsflyer:
    :path: "../../node_modules/react-native-appsflyer"
  react-native-config:
    :path: "../../node_modules/react-native-config"
  react-native-encrypted-storage:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-encrypted-storage"
  react-native-geolocation:
    :path: "../../node_modules/@react-native-community/geolocation"
  react-native-geolocation-service:
    :path: "../../node_modules/react-native-geolocation-service"
  react-native-get-random-values:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-get-random-values"
  react-native-google-maps:
    :path: "../../node_modules/react-native-maps"
  react-native-maps:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-community/netinfo"
  react-native-safe-area-context:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../../node_modules/@react-native-community/slider"
  react-native-tracking-transparency:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-tracking-transparency"
  react-native-webview:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../../node_modules/rn-fetch-blob"
  RNBootSplash:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-bootsplash"
  RNCAsyncStorage:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-async-storage/async-storage"
  RNCMaskedView:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-community/masked-view"
  RNDeviceInfo:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-device-info"
  RNFBAnalytics:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../../node_modules/@react-native-firebase/crashlytics"
  RNFBRemoteConfig:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/@react-native-firebase/remote-config"
  RNFileViewer:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-file-viewer"
  RNFS:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-gesture-handler"
  RNInAppBrowser:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-inappbrowser-reborn"
  RNReanimated:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-screens"
  RNShare:
    :path: "../../node_modules/react-native-share"
  RNSound:
    :path: "../../node_modules/react-native-sound"
  RNSVG:
    :path: "../../node_modules/@bp/pulse-mobile-sdk/node_modules/react-native-svg"
  Yoga:
    :path: "../../node_modules/react-native/ReactCommon/yoga"

CHECKOUT OPTIONS:
  cardinal-mobile-sdk:
    :commit: 4bedb95f4a75606474393b40659fd8c246e13d59
    :git: https://dev.azure.com/bp-digital/DCM_Frameworks/_git/cardinal-mobile-sdk

SPEC CHECKSUMS:
  Airship: 5dbdbd554b3e6127e4066eab3b0021f86448bd92
  AirshipFrameworkProxy: 7e602bbf4ff46d448230cfd9cd216608819239dc
  Apollo: 5e4476a3236bd576686085b7cd99655c788e4d74
  AppsFlyerFramework: 6eb4d89d2eb9a6632317f1055b359d9fd85fd5ff
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  bp-cardinal-sdk: d44a634641267a464ce819a217a5f4de06db14a3
  cardinal-mobile-sdk: 5491130b86318bb8bba2c1b8a2b7fe9a5be58de5
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 5fbbff1d7734827299274638deb8ba3024f6c597
  FBReactNativeSpec: dbbba5d9a2fb27d196fcf155ffc758c53911ab2f
  Firebase: 91fefd38712feb9186ea8996af6cbdef41473442
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAnalytics: b5efc493eb0f40ec560b04a472e3e1a15d39ca13
  FirebaseCore: 11dc8a16dfb7c5e3c3f45ba0e191a33ac4f50894
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: af38ea4adfa606f6e63fcc22091b61e7938fcf66
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseRemoteConfig: 95dddc50496b37eef199dadce850d5652b534b43
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  FirebaseSharedSwift: 20530f495084b8d840f78a100d8c5ee613375f6e
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: 3343332b18dfd5be8f1f44edd7d481ace3da4d9a
  GoogleAppMeasurement: f3abf08495ef2cba7829f15318c373b8d9226491
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 9180d43df05c1ed658a87cc733dc3044cf90c00a
  KeychainSwift: 007c4647486e4563adca839cf02cef00deb3b670
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  lottie-ios: 3d98679b41fa6fd6aff2352b3953dbd3df8a397e
  lottie-react-native: a2ae9c27c273b060b2affff2957bc0ff7fdca353
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 83bca1c184feb4d2e51c72c8369b83d641443f95
  RCTTypeSafety: 13c4a87a16d7db6cd66006ce9759f073402ef85b
  React: e67aa9f99957c7611c392b5e49355d877d6525e2
  React-callinvoker: 2790c09d964c2e5404b5410cde91b152e3746b7b
  React-Codegen: e6e05e105ca7cdb990f4d609985a2a689d8d0653
  React-Core: 9283f1e7d0d5e3d33ad298547547b1b43912534c
  React-CoreModules: 6312c9b2fec4329d9ae6a2b8c350032d1664c51b
  React-cxxreact: 7da72565656c8ac7f97c9a031d0b199bbdec0640
  React-debug: 4accb2b9dc09b575206d2c42f4082990a52ae436
  React-hermes: 1299a94f255f59a72d5baa54a2ca2e1eee104947
  React-jsi: 2208de64c3a41714ac04e86975386fc49116ea13
  React-jsiexecutor: c49502e5d02112247ee4526bc3ccfc891ae3eb9b
  React-jsinspector: 8baadae51f01d867c3921213a25ab78ab4fbcd91
  React-logger: 8edc785c47c8686c7962199a307015e2ce9a0e4f
  react-native-airship: 29b93cb63728d43774042945a1dea26f775dcecd
  react-native-appsflyer: 202a3a36d7fabe9f36198b45f952b1a91f9b27a0
  react-native-config: 7cd105e71d903104e8919261480858940a6b9c0e
  react-native-encrypted-storage: db300a3f2f0aba1e818417c1c0a6be549038deb7
  react-native-geolocation: 2d8f71eb7d0c9a0c52f336d4634444488669371d
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-get-random-values: 384787fd76976f5aec9465aff6fa9e9129af1e74
  react-native-google-maps: 356b9d01da0920be13e4c4dc83673b30e8a22c16
  react-native-maps: f699e0753c22c4d5c3a44d03895b193a4dbca6c2
  react-native-netinfo: 3aa5637c18834966e0c932de8ae1ae56fea20a97
  react-native-safe-area-context: 2cd91d532de12acdb0a9cbc8d43ac72a8e4c897c
  react-native-slider: 1cdd6ba29675df21f30544253bf7351d3c2d68c4
  react-native-tracking-transparency: 25ff1ff866e338c137c818bdec20526bb05ffcc1
  react-native-webview: 88293a0f23eca8465c0433c023ec632930e644d0
  React-NativeModulesApple: b6868ee904013a7923128892ee4a032498a1024a
  React-perflogger: 31ea61077185eb1428baf60c0db6e2886f141a5a
  React-RCTActionSheet: 392090a3abc8992eb269ef0eaa561750588fc39d
  React-RCTAnimation: 4b3cc6a29474bc0d78c4f04b52ab59bf760e8a9b
  React-RCTAppDelegate: 89b015b29885109addcabecdf3b2e833905437c7
  React-RCTBlob: 3e23dcbe6638897b5605e46d0d62955d78e8d27b
  React-RCTImage: 8a5d339d614a90a183fc1b8b6a7eb44e2e703943
  React-RCTLinking: b37dfbf646d77c326f9eae094b1fcd575b1c24c7
  React-RCTNetwork: 8bed9b2461c7d8a7d14e63df9b16181c448beebc
  React-RCTSettings: 506a5f09a455123a8873801b70aa7b4010b76b01
  React-RCTText: 3c71ecaad8ee010b79632ea2590f86c02f5cce17
  React-RCTVibration: d1b78ca38f61ea4b3e9ebb2ddbd0b5662631d99b
  React-rncore: b78b67d66ea21318f2b0611544ba076d86a74a06
  React-runtimeexecutor: 47b0a2d5bbb416db65ef881a6f7bdcfefa0001ab
  React-runtimescheduler: 7649c3b46c8dee1853691ecf60146a16ae59253c
  React-utils: 56838edeaaf651220d1e53cd0b8934fb8ce68415
  ReactCommon: 5f704096ccf7733b390f59043b6fa9cc180ee4f6
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNBootSplash: 970faaa2ab1322758c6545c88d3b23f424b86a2f
  RNCAsyncStorage: 687bb9e85dd3d45b966662440dcfc0cd962347e6
  RNCMaskedView: 0e1bc4bfa8365eba5fbbb71e07fbdc0555249489
  RNDeviceInfo: bf8a32acbcb875f568217285d1793b0e8588c974
  RNFBAnalytics: b31c136e02227f6967564f4f3c1f4796f3f7ade3
  RNFBApp: 33b2fa6d4ef5a26fe7da5edbc1a8541fa6c4564b
  RNFBCrashlytics: fe15e705e10f8b5102db7b3504e281297d469ec0
  RNFBRemoteConfig: 3b9cca12b2dced449b798144fd54a2837428c977
  RNFileViewer: ce7ca3ac370e18554d35d6355cffd7c30437c592
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 32a01c29ecc9bb0b5bf7bc0a33547f61b4dc2741
  RNInAppBrowser: e36d6935517101ccba0e875bac8ad7b0cb655364
  RNReanimated: 959792df5d10e74455276c2523a7870b39c1f0bd
  RNScreens: 3c2d122f5e08c192e254c510b212306da97d2581
  RNShare: bed7c4fbe615f3d977f22feb0902af9a790c1660
  RNSound: 6c156f925295bdc83e8e422e7d8b38d33bc71852
  RNSVG: ba3e7232f45e34b7b47e74472386cf4e1a676d0a
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 4c3aa327e4a6a23eeacd71f61c81df1bcdf677d5

PODFILE CHECKSUM: b93e773f6f6a6d50af0529fcf5afdece2b5d9296

COCOAPODS: 1.15.2
