# Custom Notification Sounds Configuration

This guide explains how to configure custom notification sounds for both Android and iOS using Airship push notifications.

## Overview

The app uses a centralized configuration system for notification sounds. All sound-related settings are managed in `src/config/notificationSound.ts` for easy maintenance and updates.

## ✅ Current Implementation Status

### Android Configuration - COMPLETE ✅
- **Sound file**: `android/app/src/main/res/raw/notification_158187.mp3` ✅
- **Notification channel**: Created in `MainApplication.java` with custom sound ✅
- **Airship config**: Default channel ID set to `bp_pulse_notifications` ✅
- **Channel ID matching**: Airship uses the pre-created channel with custom sound ✅
- **Sound loading**: Working via `useNotificationSound` hook ✅

### iOS Configuration - READY FOR XCODE SETUP ⚠️
- **Sound files**: Copied to all iOS target directories ✅
- **Sound loading code**: Enabled in `useNotificationSound.ts` ✅
- **Airship config**: Configured for iOS notifications ✅
- **Manual step required**: Add sound files to Xcode project targets ⚠️

### 🔧 Implementation Details

The custom notification sound is now automatically configured through:

1. **Centralized Configuration**: All sound settings are managed in `src/config/notificationSound.ts`
2. **Android Notification Channel**: The `MainApplication.java` creates a notification channel with the custom sound during app initialization
3. **Channel ID Matching**: The channel ID matches the Airship configuration
4. **Automatic Sound**: All notifications sent through Airship will automatically use the custom sound
5. **No Custom Keys Required**: The sound is built into the notification channel, so no additional Airship configuration is needed

### 📁 Centralized Configuration

To change the notification sound, simply update the `SOUND_FILE_NAME` in `src/config/notificationSound.ts`:

```typescript
export const NOTIFICATION_SOUND_CONFIG = {
  SOUND_FILE_NAME: 'your_new_sound_file', // Change this to update the sound
  // ... other configuration
};
```

This will automatically update:
- Android notification channel sound
- iOS sound file paths
- Airship configuration
- All components using the sound

### 🔧 Airship Dashboard Configuration

**GOOD NEWS**: For Android, no additional configuration is needed! The custom sound is automatically applied to all notifications.

#### Android - Automatic Configuration ✅
- Custom sound works automatically
- No Custom Keys needed
- No special payload required
- Just send normal push notifications through Airship

#### Optional: Using Custom Keys (if needed)
If you want to explicitly specify the sound or override it:
1. **Create Message** → **Push Notification**
2. **Optional Features** → **Custom Keys**
3. **Add Custom Key**:
   - **Platform**: `Android`
   - **Key**: `sound`
   - **Value**: `notification_158187`

#### Method 2: Using API with Platform Overrides
For API-based notifications, use this payload structure:

```json
{
  "audience": "all",
  "notification": {
    "alert": "Your message here"
  },
  "device_types": ["android", "ios"],
  "options": {
    "android": {
      "sound": "notification_158187"
    },
    "ios": {
      "sound": "notification_158187.mp3"
    }
  }
}
```

**Note**: For Android, use the filename WITHOUT the `.mp3` extension.

## iOS Configuration

### ⚠️ Files Need to be Added
- **Sound file**: Needs to be added to iOS bundle via Xcode
- **Xcode configuration**: Sound file must be added to project targets

### 📱 iOS Setup Steps

1. **Add sound file to Xcode**:
   ```bash
   # Copy the sound file to iOS resources
   cp android/app/src/main/res/raw/notification_158187 ios/notification_158187
   ```

2. **Open Xcode**:
   ```bash
   cd ios
   open bppulse.xcworkspace
   ```

3. **Add file to project**:
   - Right-click on project in Xcode
   - Select "Add Files to 'bppulse'"
   - Choose `notification_158187`
   - Ensure it's added to both app targets

4. **Verify bundle inclusion**:
   - Select the sound file in Xcode
   - Check "Target Membership" in right panel
   - Ensure both targets are checked

### 🔧 Airship Dashboard Configuration for iOS

```json
{
  "notification": {
    "alert": "Your notification message",
    "ios": {
      "sound": "notification_158187"
    }
  }
}
```

**Note**: For iOS, use the filename WITH the `.mp3` extension.

## Combined Configuration

For notifications that work on both platforms:

```json
{
  "notification": {
    "alert": "Your notification message",
    "android": {
      "sound": "charge_start"
    },
    "ios": {
      "sound": "notification_158187"
    }
  }
}
```

## Testing

### 1. Check Console Logs
Look for these messages in the app logs:
- ✅ `"Android custom sound: notification_158187 configured in raw resources"`
- ✅ `"iOS custom sound: notification_158187 should be configured in Xcode"`
- ✅ `"Custom sound file: notification_158187 is available in raw resources"`

### 2. Test Notifications
1. Send test notification from Airship dashboard
2. Include the sound configuration in payload
3. Verify custom sound plays instead of default system sound

### 3. Troubleshooting

**Android Issues**:
- Ensure `notification_158187` exists in `android/app/src/main/res/raw/`
- Check notification payload uses `"sound": "charge_start"` (no extension)
- Verify notification permissions are granted

**iOS Issues**:
- Ensure sound file is added to Xcode project
- Check file is included in app bundle
- Verify notification payload uses `"sound": "notification_158187"` (with extension)
- Confirm iOS notification permissions are granted

## Current Status

- ✅ **Android**: Fully configured and ready
- ⚠️ **iOS**: Requires Xcode configuration (sound file needs to be added to bundle)
- ✅ **Airship**: Client-side configuration complete
- 📋 **Server**: Requires payload configuration in Airship dashboard

## Next Steps

1. **For iOS**: Follow the iOS setup steps above
2. **For Testing**: Configure notification payloads in Airship dashboard
3. **For Production**: Ensure all notification campaigns include sound configuration
